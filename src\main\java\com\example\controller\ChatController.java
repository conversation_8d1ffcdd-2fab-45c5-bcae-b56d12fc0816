package com.example.controller;

import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
public class ChatController {

    private final DeepSeekChatModel chatModel;

    @Autowired
    public ChatController(DeepSeekChatModel chatModel) {
        this.chatModel = chatModel;
    }

    @GetMapping("/ai/generate")
    public Map<String, String> generate(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {
        try {
            String response = chatModel.call(message);
            return Map.of("generation", response);
        } catch (Exception e) {
            return Map.of("error", "Failed to generate response: " + e.getMessage());
        }
    }

    @GetMapping(value = "/ai/generateStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> generateStream(@RequestParam(value = "message", defaultValue = "Tell me a joke") String message) {
        try {
            var prompt = new Prompt(new UserMessage(message));
            return chatModel.stream(prompt);
        } catch (Exception e) {
            return Flux.error(e);
        }
    }

    @GetMapping("/ai/test")
    public Map<String, String> test() {
        return Map.of("status", "ChatController is working", "model", chatModel != null ? "DeepSeekChatModel injected" : "DeepSeekChatModel is null");
    }

}
